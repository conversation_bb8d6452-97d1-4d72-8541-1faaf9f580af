<script setup lang="ts">
const { data: page } = await useAsyncData(
  'blog-page',
  () => {
    return queryCollection('pages').path('/blog').first()
  }
)
if (!page.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Page not found',
    fatal: true
  })
}
const { data: posts } = await useAsyncData('blogs', () =>
  queryCollection('blog').order('date', 'DESC').all()
)
if (!posts.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'blogs posts not found',
    fatal: true
  })
}

useSeoMeta({
  title: page.value?.seo?.title || page.value?.title,
  ogTitle: page.value?.seo?.title || page.value?.title,
  description:
    page.value?.seo?.description || page.value?.description,
  ogDescription:
    page.value?.seo?.description || page.value?.description
})
</script>

<template>
  <div v-if="page">
    <!-- Hero Section -->
    <section class="py-4 sm:py-8 lg:py-12">
      <h1
        class="text-3xl sm:text-4xl lg:text-5xl font-bold text-left mb-4"
      >
        {{ page.title }}
      </h1>
      <p class="text-md text-muted text-left mb-8">
        {{ page.description }}
      </p>
    </section>

    <!-- Blog Posts Section -->
    <UPageSection
      :ui="{
        container: 'py-2 sm:py-4 lg:py-8 overflow-hidden'
      }"
    >
      <div class="space-y-8">
        <Motion
          v-for="(post, index) in posts"
          :key="index"
          :initial="{
            opacity: 0,
            transform: 'translateY(10px)'
          }"
          :while-in-view="{
            opacity: 1,
            transform: 'translateY(0)'
          }"
          :transition="{ delay: 0.2 * index }"
          :in-view-options="{ once: true }"
        >
          <UCard
            class="group overflow-visible transition-all duration-300"
          >
            <NuxtLink
              :to="post.path"
              class="md:grid md:grid-cols-2 gap-6"
            >
              <div
                class="overflow-visible"
                :class="
                  index % 2 === 0
                    ? 'sm:-rotate-1'
                    : 'sm:rotate-1'
                "
              >
                <img
                  v-if="post.image"
                  :src="post.image"
                  :alt="post.title"
                  class="group-hover:scale-105 rounded-lg shadow-lg border-4 border-muted ring-2 ring-default w-full h-48 object-cover transition-transform duration-300"
                />
              </div>
              <div class="p-6">
                <div class="text-sm text-muted mb-2">
                  {{
                    new Date(post.date).toLocaleDateString()
                  }}
                </div>
                <h3 class="text-xl font-semibold mb-2">
                  {{ post.title }}
                </h3>
                <p class="text-muted">
                  {{ post.description }}
                </p>
              </div>
            </NuxtLink>
          </UCard>
        </Motion>
      </div>
    </UPageSection>
  </div>
</template>
